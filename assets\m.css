

#app {
    max-width: 600px;
    margin: 30px auto;
	background-color: #f4f6f8;
	font-size: 16px;
	font-family: PingFangSC-Regular;
	color: #222;
	box-shadow: 0 10px 20px 0 rgba(236, 236, 236, 0.86);
    border-radius: 20px;
    padding:30px;
}



@media (max-width: 600px) {
	#app{
		width: 100%;
	        margin: 0 auto;
	        height:100%;
	        border-radius: 0px;
	         padding:0px;
	}
   }






/*! normalize.css v5.0.0 | MIT License | github.com/necolas/normalize.css */

html {
	font-family: sans-serif;
	line-height: 1.15;
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%
}

body {
	background: #ff4b1f;  /* fallback for old browsers */
	background: -webkit-linear-gradient(to right, #1fddff, #ff4b1f);  /* Chrome 10-25, Safari 5.1-6 */
	background: linear-gradient(to right, #1fddff, #ff4b1f); /* W3C, I<PERSON> 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

	/* background: url("https://img.imdodo.com/679e0419e68dead05c216615997c4f90.jpg"); */
	background-size: cover;
	background-position: center;
	margin: 0
}

article,
aside,
footer,
header,
nav,
section {
	display: block
}

h1 {
	font-size: 2em;
	margin: .67em 0
}

figcaption,
figure,
main {
	display: block
}

figure {
	margin: 1em 40px
}

hr {
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
	height: 0;
	overflow: visible
}

pre {
	font-family: monospace, monospace;
	font-size: 1em
}

a {
	background-color: transparent;
	-webkit-text-decoration-skip: objects
}

a:active,
a:hover {
	outline-width: 0
}

abbr[title] {
	border-bottom: none;
	text-decoration: underline;
	-webkit-text-decoration: underline dotted;
	text-decoration: underline dotted
}

b,
strong {
	font-weight: inherit;
	font-weight: bolder
}

code,
kbd,
samp {
	font-family: monospace, monospace;
	font-size: 1em
}

dfn {
	font-style: italic
}

mark {
	background-color: #ff0;
	color: #000
}

small {
	font-size: 80%
}

sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline
}

sub {
	bottom: -.25em
}

sup {
	top: -.5em
}

audio,
video {
	display: inline-block
}

audio:not([controls]) {
	display: none;
	height: 0
}

img {
	border-style: none
}

svg:not(:root) {
	overflow: hidden
}

button,
input,
optgroup,
select,
textarea {
	font-family: sans-serif;
	font-size: 100%;
	line-height: 1.15;
	margin: 0
}

button,
input {
	overflow: visible
}

button,
select {
	text-transform: none
}

[type=reset],
[type=submit],
button,
html [type=button] {
	-webkit-appearance: button
}

[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner,
button::-moz-focus-inner {
	border-style: none;
	padding: 0
}

[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring,
button:-moz-focusring {
	outline: 1px dotted ButtonText
}

fieldset {
	border: 1px solid silver;
	margin: 0 2px;
	padding: .35em .625em .75em
}

legend {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	color: inherit;
	display: table;
	max-width: 100%;
	padding: 0;
	white-space: normal
}

progress {
	display: inline-block;
	vertical-align: baseline
}

textarea {
	overflow: auto;
	resize: vertical
}

[type=checkbox],
[type=radio] {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	padding: 0
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
	height: auto
}

[type=search] {
	-webkit-appearance: textfield;
	outline-offset: -2px
}

[type=search]::-webkit-search-cancel-button,
[type=search]::-webkit-search-decoration {
	-webkit-appearance: none
}

::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit
}

details,
menu {
	display: block
}

summary {
	display: list-item
}

canvas {
	display: inline-block
}

[hidden],
template {
	display: none
}

* {
	-webkit-tap-highlight-color: transparent
}

*,
:after,
:before {
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

body {
	font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
	font-size: 12px;
	line-height: 1.5;
	color: #515a6e;
	background-color: #fff;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale
}

article,
aside,
blockquote,
body,
button,
dd,
details,
div,
dl,
dt,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
hr,
input,
legend,
li,
menu,
nav,
ol,
p,
section,
td,
textarea,
th,
ul {
	margin: 0;
	padding: 0
}

button,
input,
select,
textarea {
	font-family: inherit;
	font-size: inherit;
	line-height: inherit
}

input::-ms-clear,
input::-ms-reveal {
	display: none
}

a {
	color: #f55d54;
	background: 0 0;
	text-decoration: none;
	outline: 0;
	cursor: pointer;
	-webkit-transition: color .2s ease;
	transition: color .2s ease
}

a:hover {
	color: #57a3f3
}

a:active {
	color: #2b85e4
}

a:active,
a:hover {
	outline: 0;
	text-decoration: none
}

a[disabled] {
	color: #ccc;
	cursor: not-allowed;
	pointer-events: none
}

code,
kbd,
pre,
samp {
	font-family: Consolas, Menlo, Courier, monospace
}

.constellation-icon {
	display: inline-block;
	width: 22px;
	height: 22px;
	background-size: contain;
	background-repeat: no-repeat;
	background-position: 50%
}

.baiyang {
	background-image: url(../img/baiyang.9671921a.jpg)
}

.jinniu {
	background-image: url(../img/jinniu.0344f364.jpg)
}

.shuangzi {
	background-image: url(../img/shuangzi.b653cb1e.jpg)
}

.juxie {
	background-image: url(../img/juxie.4b584c57.jpg)
}

.shizi {
	background-image: url(../img/shizi.fa459ceb.jpg)
}

.chunv {
	background-image: url(../img/chunv.36494d9b.jpg)
}

.tiancheng {
	background-image: url(../img/tiancheng.fcc3ace2.jpg)
}

.tianxie {
	background-image: url(../img/tianxie.7748b67d.jpg)
}

.sheshou {
	background-image: url(../img/sheshou.13905cc2.jpg)
}

.mojie {
	background-image: url(../img/mojie.ae57606b.jpg)
}

.shuiping {
	background-image: url(../img/shuiping.1e58f191.jpg)
}

.shuangyu {
	background-image: url(../img/shuangyu.c03c5013.jpg)
}

.wrap[data-v-1cb8f412] {
	width: 100%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	padding: 16px 16px 0
}

.wrap a[data-v-1cb8f412] {
	font-size: 12px;
	line-height: 18px;
	color: #666
}
